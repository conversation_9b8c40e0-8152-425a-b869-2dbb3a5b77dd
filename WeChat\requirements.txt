# WeChat 适配器依赖配置
# 兼容 Python 3.11-3.12.10，基于实际测试的稳定版本

# Flask Web 框架核心组件
blinker>=1.6.2,<2.0.0
Flask>=2.3.3,<4.0.0
flask-cors>=4.0.0,<5.0.0
itsdangerous>=2.1.2,<3.0.0
Jinja2>=3.1.2,<4.0.0
MarkupSafe>=2.1.3,<3.0.0
Werkzeug>=2.3.7,<4.0.0

# HTTP 和网络通信
certifi>=2023.7.22
charset-normalizer>=3.2.0,<4.0.0
idna>=3.4,<4.0.0
requests>=2.31.0,<3.0.0
urllib3>=2.0.4,<3.0.0

# 系统工具和命令行
click>=8.1.7,<9.0.0
colorama>=0.4.6
psutil>=5.9.5,<6.0.0
pyperclip>=1.8.2,<2.0.0
tenacity>=8.2.3,<9.0.0
watchdog>=3.0.0,<5.0.0

# Windows 系统集成
comtypes>=1.2.0,<2.0.0
pywin32>=306

# 图像处理库
pillow>=10.0.1,<12.0.0

# 微信机器人核心 (动态版本选择)
./libs/wxautox_wechatbot-39.1.1-cp311-cp311-win_amd64.whl
